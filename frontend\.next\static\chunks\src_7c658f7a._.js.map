{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/learning/frontend/src/lib/auth.ts"], "sourcesContent": ["import axios from 'axios';\r\n\r\nconst API_BASE_URL = 'http://localhost:8000/api';\r\n\r\n// <PERSON>ie helper functions\r\nconst setCookie = (name: string, value: string, days: number = 1) => {\r\n  if (typeof window === 'undefined') return;\r\n  const expires = new Date();\r\n  expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));\r\n  document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/`;\r\n};\r\n\r\nconst getCookie = (name: string): string | null => {\r\n  if (typeof window === 'undefined') return null;\r\n  const nameEQ = name + \"=\";\r\n  const ca = document.cookie.split(';');\r\n  for (let i = 0; i < ca.length; i++) {\r\n    let c = ca[i];\r\n    while (c.charAt(0) === ' ') c = c.substring(1, c.length);\r\n    if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);\r\n  }\r\n  return null;\r\n};\r\n\r\nconst deleteCookie = (name: string) => {\r\n  if (typeof window === 'undefined') return;\r\n  document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 UTC;path=/;`;\r\n};\r\n\r\nconst clearAllData = () => {\r\n  if (typeof window === 'undefined') return;\r\n\r\n  // Clear localStorage\r\n  localStorage.clear();\r\n\r\n  // Clear sessionStorage\r\n  sessionStorage.clear();\r\n\r\n  // Clear all cookies\r\n  const cookies = document.cookie.split(\";\");\r\n  for (let cookie of cookies) {\r\n    const eqPos = cookie.indexOf(\"=\");\r\n    const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();\r\n    deleteCookie(name);\r\n  }\r\n};\r\n\r\nexport interface LoginCredentials {\r\n  username: string;\r\n  password: string;\r\n}\r\n\r\nexport interface LoginResponse {\r\n  access_token: string;\r\n  token_type: string;\r\n  message: string;\r\n}\r\n\r\nexport interface User {\r\n  username: string;\r\n}\r\n\r\nclass AuthService {\r\n  private user: User | null = null;\r\n\r\n  constructor() {\r\n    if (typeof window !== 'undefined') {\r\n      const userData = localStorage.getItem('user_data');\r\n      const isLoggedIn = getCookie('user_session') === 'true';\r\n\r\n      if (userData && isLoggedIn) {\r\n        try {\r\n          this.user = JSON.parse(userData);\r\n        } catch (error) {\r\n          console.error('Error parsing user data:', error);\r\n          this.clearSession();\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  async login(credentials: LoginCredentials): Promise<LoginResponse> {\r\n    try {\r\n      const response = await axios.post(`${API_BASE_URL}/auth/login`, credentials);\r\n      const data = response.data;\r\n\r\n      // Store user session\r\n      this.user = { username: credentials.username };\r\n      if (typeof window !== 'undefined') {\r\n        localStorage.setItem('user_data', JSON.stringify(this.user));\r\n        setCookie('user_session', 'true', 1); // Simple session flag\r\n      }\r\n\r\n      return data;\r\n    } catch (error: any) {\r\n      throw new Error(error.response?.data?.detail || 'Login failed');\r\n    }\r\n  }\r\n\r\n  async logout(): Promise<void> {\r\n    try {\r\n      // Simple logout - just clear session\r\n      await axios.post(`${API_BASE_URL}/auth/logout`);\r\n    } catch (error) {\r\n      console.error('Logout error:', error);\r\n    } finally {\r\n      this.clearSession();\r\n    }\r\n  }\r\n\r\n  private clearSession(): void {\r\n    this.user = null;\r\n    if (typeof window !== 'undefined') {\r\n      localStorage.removeItem('user_data');\r\n      deleteCookie('user_session');\r\n    }\r\n  }\r\n\r\n  async verifyAuth(): Promise<User | null> {\r\n    // Simple verification - just check if session exists\r\n    if (typeof window !== 'undefined') {\r\n      const isLoggedIn = getCookie('user_session') === 'true';\r\n      const userData = localStorage.getItem('user_data');\r\n\r\n      if (isLoggedIn && userData) {\r\n        try {\r\n          this.user = JSON.parse(userData);\r\n          return this.user;\r\n        } catch (error) {\r\n          this.clearSession();\r\n          return null;\r\n        }\r\n      }\r\n    }\r\n\r\n    return this.user;\r\n  }\r\n\r\n  getUser(): User | null {\r\n    return this.user;\r\n  }\r\n\r\n  isAuthenticated(): boolean {\r\n    if (typeof window !== 'undefined') {\r\n      return getCookie('user_session') === 'true' && !!localStorage.getItem('user_data');\r\n    }\r\n    return !!this.user;\r\n  }\r\n}\r\n\r\nexport const authService = new AuthService();\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,MAAM,eAAe;AAErB,0BAA0B;AAC1B,MAAM,YAAY,SAAC,MAAc;QAAe,wEAAe;IAC7D;;IACA,MAAM,UAAU,IAAI;IACpB,QAAQ,OAAO,CAAC,QAAQ,OAAO,KAAM,OAAO,KAAK,KAAK,KAAK;IAC3D,SAAS,MAAM,GAAG,AAAC,GAAU,OAAR,MAAK,KAAoB,OAAjB,OAAM,aAAiC,OAAtB,QAAQ,WAAW,IAAG;AACtE;AAEA,MAAM,YAAY,CAAC;IACjB;;IACA,MAAM,SAAS,OAAO;IACtB,MAAM,KAAK,SAAS,MAAM,CAAC,KAAK,CAAC;IACjC,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,MAAM,EAAE,IAAK;QAClC,IAAI,IAAI,EAAE,CAAC,EAAE;QACb,MAAO,EAAE,MAAM,CAAC,OAAO,IAAK,IAAI,EAAE,SAAS,CAAC,GAAG,EAAE,MAAM;QACvD,IAAI,EAAE,OAAO,CAAC,YAAY,GAAG,OAAO,EAAE,SAAS,CAAC,OAAO,MAAM,EAAE,EAAE,MAAM;IACzE;IACA,OAAO;AACT;AAEA,MAAM,eAAe,CAAC;IACpB;;IACA,SAAS,MAAM,GAAG,AAAC,GAAO,OAAL,MAAK;AAC5B;AAEA,MAAM,eAAe;IACnB;;IAEA,qBAAqB;IACrB,aAAa,KAAK;IAElB,uBAAuB;IACvB,eAAe,KAAK;IAEpB,oBAAoB;IACpB,MAAM,UAAU,SAAS,MAAM,CAAC,KAAK,CAAC;IACtC,KAAK,IAAI,UAAU,QAAS;QAC1B,MAAM,QAAQ,OAAO,OAAO,CAAC;QAC7B,MAAM,OAAO,QAAQ,CAAC,IAAI,OAAO,MAAM,CAAC,GAAG,OAAO,IAAI,KAAK,OAAO,IAAI;QACtE,aAAa;IACf;AACF;AAiBA,MAAM;IAmBJ,MAAM,MAAM,WAA6B,EAA0B;QACjE,IAAI;YACF,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,AAAC,GAAe,OAAb,cAAa,gBAAc;YAChE,MAAM,OAAO,SAAS,IAAI;YAE1B,qBAAqB;YACrB,IAAI,CAAC,IAAI,GAAG;gBAAE,UAAU,YAAY,QAAQ;YAAC;YAC7C,wCAAmC;gBACjC,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC,IAAI,CAAC,IAAI;gBAC1D,UAAU,gBAAgB,QAAQ,IAAI,sBAAsB;YAC9D;YAEA,OAAO;QACT,EAAE,OAAO,OAAY;gBACH,sBAAA;YAAhB,MAAM,IAAI,MAAM,EAAA,kBAAA,MAAM,QAAQ,cAAd,uCAAA,uBAAA,gBAAgB,IAAI,cAApB,2CAAA,qBAAsB,MAAM,KAAI;QAClD;IACF;IAEA,MAAM,SAAwB;QAC5B,IAAI;YACF,qCAAqC;YACrC,MAAM,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,AAAC,GAAe,OAAb,cAAa;QACnC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,IAAI,CAAC,YAAY;QACnB;IACF;IAEQ,eAAqB;QAC3B,IAAI,CAAC,IAAI,GAAG;QACZ,wCAAmC;YACjC,aAAa,UAAU,CAAC;YACxB,aAAa;QACf;IACF;IAEA,MAAM,aAAmC;QACvC,qDAAqD;QACrD,wCAAmC;YACjC,MAAM,aAAa,UAAU,oBAAoB;YACjD,MAAM,WAAW,aAAa,OAAO,CAAC;YAEtC,IAAI,cAAc,UAAU;gBAC1B,IAAI;oBACF,IAAI,CAAC,IAAI,GAAG,KAAK,KAAK,CAAC;oBACvB,OAAO,IAAI,CAAC,IAAI;gBAClB,EAAE,OAAO,OAAO;oBACd,IAAI,CAAC,YAAY;oBACjB,OAAO;gBACT;YACF;QACF;QAEA,OAAO,IAAI,CAAC,IAAI;IAClB;IAEA,UAAuB;QACrB,OAAO,IAAI,CAAC,IAAI;IAClB;IAEA,kBAA2B;QACzB,wCAAmC;YACjC,OAAO,UAAU,oBAAoB,UAAU,CAAC,CAAC,aAAa,OAAO,CAAC;QACxE;;;IAEF;IAlFA,aAAc;QAFd,+KAAQ,QAAoB;QAG1B,wCAAmC;YACjC,MAAM,WAAW,aAAa,OAAO,CAAC;YACtC,MAAM,aAAa,UAAU,oBAAoB;YAEjD,IAAI,YAAY,YAAY;gBAC1B,IAAI;oBACF,IAAI,CAAC,IAAI,GAAG,KAAK,KAAK,CAAC;gBACzB,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,4BAA4B;oBAC1C,IAAI,CAAC,YAAY;gBACnB;YACF;QACF;IACF;AAqEF;AAEO,MAAM,cAAc,IAAI", "debugId": null}}, {"offset": {"line": 145, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/learning/frontend/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { createContext, useContext, useEffect, useState } from 'react';\r\nimport { authService, User } from '@/lib/auth';\r\n\r\ninterface AuthContextType {\r\n  user: User | null;\r\n  loading: boolean;\r\n  login: (username: string, password: string) => Promise<void>;\r\n  logout: () => Promise<void>;\r\n  isAuthenticated: boolean;\r\n}\r\n\r\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\r\n\r\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\r\n  const [user, setUser] = useState<User | null>(null);\r\n  const [loading, setLoading] = useState(true);\r\n\r\n  useEffect(() => {\r\n    checkAuth();\r\n  }, []);\r\n\r\n  const checkAuth = async () => {\r\n    try {\r\n      setLoading(true);\r\n      const userData = await authService.verifyAuth();\r\n      setUser(userData);\r\n    } catch (error) {\r\n      console.error('Auth verification failed:', error);\r\n      setUser(null);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const login = async (username: string, password: string) => {\r\n    try {\r\n      await authService.login({ username, password });\r\n      await checkAuth();\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  };\r\n\r\n  const logout = async () => {\r\n    await authService.logout();\r\n    setUser(null);\r\n  };\r\n\r\n  const value = {\r\n    user,\r\n    loading,\r\n    login,\r\n    logout,\r\n    isAuthenticated: !!user,\r\n  };\r\n\r\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;\r\n}\r\n\r\nexport function useAuth() {\r\n  const context = useContext(AuthContext);\r\n  if (context === undefined) {\r\n    throw new Error('useAuth must be used within an AuthProvider');\r\n  }\r\n  return context;\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAHA;;;AAaA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,KAA2C;QAA3C,EAAE,QAAQ,EAAiC,GAA3C;;IAC3B,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR;QACF;iCAAG,EAAE;IAEL,MAAM,YAAY;QAChB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,qHAAA,CAAA,cAAW,CAAC,UAAU;YAC7C,QAAQ;QACV,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,QAAQ,OAAO,UAAkB;QACrC,IAAI;YACF,MAAM,qHAAA,CAAA,cAAW,CAAC,KAAK,CAAC;gBAAE;gBAAU;YAAS;YAC7C,MAAM;QACR,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,MAAM,SAAS;QACb,MAAM,qHAAA,CAAA,cAAW,CAAC,MAAM;QACxB,QAAQ;IACV;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA,iBAAiB,CAAC,CAAC;IACrB;IAEA,qBAAO,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAC9C;GA5CgB;KAAA;AA8CT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}, {"offset": {"line": 233, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/learning/frontend/src/components/DataCleaner.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect } from 'react';\r\n\r\nexport default function DataCleaner() {\r\n  useEffect(() => {\r\n    // Only clear data in development mode or when explicitly needed\r\n    // This prevents clearing user authentication data on every page load\r\n    const shouldClearData = process.env.NODE_ENV === 'development' &&\r\n                           localStorage.getItem('force_clear_data') === 'true';\r\n\r\n    if (shouldClearData) {\r\n      const clearAllData = () => {\r\n        if (typeof window === 'undefined') return;\r\n\r\n        // Clear localStorage\r\n        localStorage.clear();\r\n\r\n        // Clear sessionStorage\r\n        sessionStorage.clear();\r\n\r\n        // Clear all cookies\r\n        const cookies = document.cookie.split(\";\");\r\n        for (let cookie of cookies) {\r\n          const eqPos = cookie.indexOf(\"=\");\r\n          const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();\r\n          document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 UTC;path=/;`;\r\n        }\r\n\r\n        // Remove the flag so it doesn't clear again\r\n        localStorage.removeItem('force_clear_data');\r\n      };\r\n\r\n      clearAllData();\r\n    }\r\n  }, []);\r\n\r\n  return null; // This component doesn't render anything\r\n}\r\n"], "names": [], "mappings": ";;;AAQ4B;AAN5B;;AAFA;;AAIe,SAAS;;IACtB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,gEAAgE;YAChE,qEAAqE;YACrE,MAAM,kBAAkB,oDAAyB,iBAC1B,aAAa,OAAO,CAAC,wBAAwB;YAEpE,IAAI,iBAAiB;gBACnB,MAAM;0DAAe;wBACnB;;wBAEA,qBAAqB;wBACrB,aAAa,KAAK;wBAElB,uBAAuB;wBACvB,eAAe,KAAK;wBAEpB,oBAAoB;wBACpB,MAAM,UAAU,SAAS,MAAM,CAAC,KAAK,CAAC;wBACtC,KAAK,IAAI,UAAU,QAAS;4BAC1B,MAAM,QAAQ,OAAO,OAAO,CAAC;4BAC7B,MAAM,OAAO,QAAQ,CAAC,IAAI,OAAO,MAAM,CAAC,GAAG,OAAO,IAAI,KAAK,OAAO,IAAI;4BACtE,SAAS,MAAM,GAAG,AAAC,GAAO,OAAL,MAAK;wBAC5B;wBAEA,4CAA4C;wBAC5C,aAAa,UAAU,CAAC;oBAC1B;;gBAEA;YACF;QACF;gCAAG,EAAE;IAEL,OAAO,MAAM,yCAAyC;AACxD;GAlCwB;KAAA", "debugId": null}}]}